<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复版本测试 - 抖音视频下载工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px 30px;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .url-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .url-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            flex: 1;
            min-width: 150px;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result-section {
            margin-top: 30px;
            display: none;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
        }
        
        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .video-info {
            display: grid;
            gap: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e1e8ed;
        }
        
        .info-label {
            font-weight: 600;
            color: #666;
        }
        
        .info-value {
            color: #333;
            word-break: break-all;
        }
        
        .copy-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            margin-left: 10px;
        }
        
        .copy-btn:hover {
            background: #218838;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #dc3545;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #28a745;
        }
        
        .usage-guide {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .usage-guide h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .usage-guide ul {
            list-style: none;
            padding-left: 0;
        }
        
        .usage-guide li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        
        .usage-guide li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #1976d2;
            font-weight: bold;
        }
        
        .test-info {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 5px solid #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 抖音视频下载工具</h1>
            <p>修复版本测试 - 验证字符串转义问题是否解决</p>
        </div>
        
        <div class="main-content">
            <div class="test-info">
                <strong>🧪 测试说明：</strong>
                <br>这是修复版本的测试页面，用于验证字符串转义问题是否已解决。
                <br>主要修复：将单引号转义改为使用 HTML 实体编码 (&quot;)
            </div>
            
            <div class="input-section">
                <div class="input-group">
                    <label for="videoUrl">请输入抖音视频分享链接：</label>
                    <input 
                        type="text" 
                        id="videoUrl" 
                        class="url-input" 
                        placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                        value="https://v.douyin.com/test123/"
                    >
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="getVideoUrl()">
                        📥 获取视频链接
                    </button>
                    <button class="btn btn-secondary" onclick="getVideoInfo()">
                        📊 获取详细信息
                    </button>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在解析视频信息，请稍候...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <div class="result-title" id="resultTitle">解析结果</div>
                    <div id="resultContent"></div>
                </div>
            </div>
            
            <div class="usage-guide">
                <h3>🔧 修复内容</h3>
                <ul>
                    <li>修复了 copyToClipboard 函数的字符串转义问题</li>
                    <li>将单引号转义 (\') 改为 HTML 实体编码 (&quot;)</li>
                    <li>避免了多层嵌套字符串的转义冲突</li>
                    <li>保持了原版本的所有功能和样式</li>
                    <li>测试按钮功能是否正常响应</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        console.log('修复版本 JavaScript 已加载');
        
        // 提取URL的正则表达式
        const urlRegex = /https?:\/\/v\.douyin\.com\/[A-Za-z0-9]+\/?/;
        
        // 显示加载状态
        function showLoading() {
            console.log('显示加载状态');
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }
        
        // 隐藏加载状态
        function hideLoading() {
            console.log('隐藏加载状态');
            document.getElementById('loading').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }
        
        // 显示结果
        function showResult(title, content) {
            console.log('显示结果:', title);
            document.getElementById('resultTitle').textContent = title;
            document.getElementById('resultContent').innerHTML = content;
            document.getElementById('resultSection').style.display = 'block';
        }
        
        // 显示错误信息
        function showError(message) {
            console.log('显示错误:', message);
            const errorHtml = '<div class="error-message">❌ ' + message + '</div>';
            showResult('解析失败', errorHtml);
        }
        
        // 显示成功信息
        function showSuccess(message) {
            console.log('显示成功:', message);
            const successHtml = '<div class="success-message">✅ ' + message + '</div>';
            showResult('解析成功', successHtml);
        }
        
        // 复制到剪贴板 - 修复版本
        function copyToClipboard(text) {
            console.log('复制到剪贴板 (修复版):', text);
            try {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        alert('✅ 已复制到剪贴板！\n修复版本工作正常');
                    });
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('✅ 已复制到剪贴板！\n修复版本工作正常');
                }
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }
        
        // 提取并验证URL
        function extractAndValidateUrl() {
            console.log('提取并验证URL');
            const input = document.getElementById('videoUrl').value.trim();
            console.log('输入内容:', input);
            
            if (!input) {
                showError('请输入抖音视频链接');
                return null;
            }
            
            // 尝试从输入文本中提取URL
            const match = input.match(urlRegex);
            if (match) {
                console.log('找到匹配的URL:', match[0]);
                return match[0];
            }
            
            // 如果直接是URL格式
            if (input.startsWith('http')) {
                console.log('直接URL格式:', input);
                return input;
            }

            showError('未找到有效的抖音视频链接，请检查输入格式');
            return null;
        }
        
        // 获取视频链接 - 修复版本
        function getVideoUrl() {
            console.log('getVideoUrl 函数被调用 (修复版)');
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            // 模拟网络请求
            setTimeout(() => {
                hideLoading();
                
                // 测试包含特殊字符的URL
                const mockResult = 'https://example.com/video.mp4?title=It\'s a "test" video&id=123';
                
                // 使用修复后的转义方法
                const copyButton = '<button class="copy-btn" onclick="copyToClipboard(&quot;' + mockResult.replace(/"/g, '&quot;') + '&quot;)">复制链接</button>';
                const content = 
                    '<div class="info-item">' +
                        '<span class="info-label">视频下载链接：</span>' +
                        '<span class="info-value">' + mockResult + ' ' + copyButton + '</span>' +
                    '</div>' +
                    '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px;">' +
                        '💡 提示：这是修复版本的测试。点击复制按钮测试转义是否正确。' +
                    '</div>';
                showResult('视频链接获取成功 (修复版)', content);
            }, 1500);
        }
        
        // 获取视频详细信息 - 修复版本
        function getVideoInfo() {
            console.log('getVideoInfo 函数被调用 (修复版)');
            const url = extractAndValidateUrl();
            if (!url) return;

            showLoading();

            // 模拟网络请求
            setTimeout(() => {
                hideLoading();
                
                // 模拟包含特殊字符的视频信息
                const mockVideoInfo = {
                    aweme_id: '7123456789012345678',
                    desc: 'This is a "test" video with \'quotes\'',
                    nickname: 'Test User',
                    signature: 'I\'m a "test" user',
                    create_time: '2024-01-15 14:30:25',
                    digg_count: 12345,
                    comment_count: 678,
                    share_count: 234,
                    collect_count: 89,
                    video_url: 'https://example.com/video.mp4?title=It\'s a "test" video&id=123'
                };
                
                // 使用修复后的转义方法
                const videoUrl = mockVideoInfo.video_url || '';
                const copyButton = '<button class="copy-btn" onclick="copyToClipboard(&quot;' + videoUrl.replace(/"/g, '&quot;') + '&quot;)">复制链接</button>';

                const content = 
                    '<div class="video-info">' +
                        '<div class="info-item">' +
                            '<span class="info-label">视频ID：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.aweme_id || '未知') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">标题：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.desc || '无标题') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">作者：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.nickname || '未知') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">作者签名：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.signature || '无签名') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">创建时间：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.create_time || '未知') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">点赞数：</span>' +
                            '<span class="info-value">' + ((mockVideoInfo.digg_count && mockVideoInfo.digg_count.toLocaleString()) || '0') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">评论数：</span>' +
                            '<span class="info-value">' + ((mockVideoInfo.comment_count && mockVideoInfo.comment_count.toLocaleString()) || '0') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">分享数：</span>' +
                            '<span class="info-value">' + ((mockVideoInfo.share_count && mockVideoInfo.share_count.toLocaleString()) || '0') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">收藏数：</span>' +
                            '<span class="info-value">' + ((mockVideoInfo.collect_count && mockVideoInfo.collect_count.toLocaleString()) || '0') + '</span>' +
                        '</div>' +
                        '<div class="info-item">' +
                            '<span class="info-label">下载链接：</span>' +
                            '<span class="info-value">' + (mockVideoInfo.video_url || '获取失败') + ' ' + (mockVideoInfo.video_url ? copyButton : '') + '</span>' +
                        '</div>' +
                    '</div>' +
                    '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px;">' +
                        '💡 提示：这是修复版本的测试。注意标题和签名中包含特殊字符，测试转义是否正确。' +
                    '</div>';
                showResult('视频详细信息 (修复版)', content);
            }, 2000);
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('修复版本页面加载完成');
            
            // 回车键支持
            const urlInput = document.getElementById('videoUrl');
            if (urlInput) {
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        getVideoUrl();
                    }
                });
                
                // 自动聚焦输入框
                urlInput.focus();
            }
            
            // 显示初始测试信息
            setTimeout(() => {
                showSuccess('修复版本加载完成！可以测试按钮功能和复制功能。特别注意测试包含引号的URL是否能正确复制。');
            }, 1000);
        });
    </script>
</body>
</html>
