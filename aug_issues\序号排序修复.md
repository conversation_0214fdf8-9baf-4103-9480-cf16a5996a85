# WMS20250620.py 序号排序修复任务

## 问题描述
序号列显示顺序错误：1, 10, 11, 2, 3, 4, 5, 6, 7, 8, 9
原因：序号作为字符串排序，导致字典序排序而非数字排序

## 解决方案（修订）
最终采用SQL ROW_NUMBER()方案：在数据库查询中直接生成数字序号
- 数据库生成：1, 2, 3, 10, 11...（数字类型）
- 显示效果：1, 2, 3, 10, 11...（正常数字显示）
- 排序结果：1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11...（正确的数字排序）
- 优势：代码简洁，与进销存窗口排序逻辑统一

## 修复计划（修订）
1. 修改4个SQL查询，使用ROW_NUMBER()生成序号
2. 删除Python中的序号生成逻辑
3. 删除自定义代理类和相关设置
4. 修复数据索引引用
5. 验证功能正常

## 修改文件
- WMS20250620.py

## 修改位置
- 第557-565行：修改base_select_dd SQL查询，添加ROW_NUMBER()
- 第620-622行：修改get_chaxun_PF_PHLRHZ_data SQL查询
- 第659-668行：修改get_chaxun_PF_PHLRMX_data SQL查询
- 第716-718行：修改get_chaxun_DBMX_KP_data SQL查询
- 第589-616行：删除Python序号生成逻辑
- 第2102-2113行：删除SequenceNumberDelegate类
- 第2238-2299行：删除序号列代理设置

## 执行状态
- [x] 修改SQL查询使用ROW_NUMBER()
- [x] 删除Python序号生成逻辑
- [x] 删除自定义代理类
- [x] 修复数据索引引用
- [ ] 测试验证

## 修改详情
1. 修改了4个SQL查询，使用ROW_NUMBER()生成序号：
   - get_chaxun_dd_data(): 添加 ROW_NUMBER() OVER (ORDER BY jz.djbh)
   - get_chaxun_PF_PHLRHZ_data(): 添加 ROW_NUMBER() OVER (ORDER BY kaipiaodjbh)
   - get_chaxun_PF_PHLRMX_data(): 添加 ROW_NUMBER() OVER (ORDER BY PF_PHLRMX.hw)
   - get_chaxun_DBMX_KP_data(): 添加 ROW_NUMBER() OVER (ORDER BY DBHZ_KP.djbh)

2. 删除了Python中的序号生成逻辑：
   - 移除 enumerate() 循环
   - 移除 f"{row_idx + 1:03d}" 格式化
   - 直接使用数据库返回的数据

3. 删除了臃肿的代理类和设置：
   - 删除 SequenceNumberDelegate 类
   - 删除所有 setItemDelegateForColumn(0, sequence_number_delegate) 调用

4. 修复了数据索引引用：
   - 调整了颜色高亮逻辑中的索引
   - 修复了单据编号获取的索引
