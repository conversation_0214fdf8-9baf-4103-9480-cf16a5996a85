const http = require('http');
const url = require('url');

// 生成HTML页面
function generateHTML() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音视频无水印下载工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px 30px;
        }
        
        .input-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .url-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .url-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .btn {
            flex: 1;
            min-width: 150px;
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }
        
        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .result-section {
            margin-top: 30px;
            display: none;
        }
        
        .result-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            border-left: 5px solid #667eea;
        }
        
        .result-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #dc3545;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            border-left: 5px solid #28a745;
        }
        
        .usage-guide {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        
        .usage-guide h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .usage-guide ul {
            list-style: none;
            padding-left: 0;
        }
        
        .usage-guide li {
            margin-bottom: 10px;
            padding-left: 20px;
            position: relative;
        }
        
        .usage-guide li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #1976d2;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 抖音视频下载工具</h1>
            <p>快速获取无水印视频链接和详细信息</p>
        </div>
        
        <div class="main-content">
            <div class="input-section">
                <div class="input-group">
                    <label for="videoUrl">请输入抖音视频分享链接：</label>
                    <input 
                        type="text" 
                        id="videoUrl" 
                        class="url-input" 
                        placeholder="例如：https://v.douyin.com/xxxx/ 或复制分享的完整文本"
                    >
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="testButton('获取视频链接')">
                        📥 获取视频链接
                    </button>
                    <button class="btn btn-secondary" onclick="testButton('获取详细信息')">
                        📊 获取详细信息
                    </button>
                </div>
            </div>
            
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在解析视频信息，请稍候...</p>
            </div>
            
            <div class="result-section" id="resultSection">
                <div class="result-card">
                    <div class="result-title" id="resultTitle">解析结果</div>
                    <div id="resultContent"></div>
                </div>
            </div>
            
            <div class="usage-guide">
                <h3>📖 使用说明</h3>
                <ul>
                    <li>支持抖音APP分享的短链接（如：https://v.douyin.com/xxxx/）</li>
                    <li>可以直接粘贴抖音分享的完整文本，系统会自动提取链接</li>
                    <li>"获取视频链接"返回可直接下载的无水印视频地址</li>
                    <li>"获取详细信息"返回视频的完整元数据信息</li>
                    <li>点击复制按钮可快速复制结果到剪贴板</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        console.log('JavaScript 已加载 - 简化版本');
        
        // 显示加载状态
        function showLoading() {
            console.log('显示加载状态');
            document.getElementById('loading').style.display = 'block';
            document.getElementById('resultSection').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = true);
        }
        
        // 隐藏加载状态
        function hideLoading() {
            console.log('隐藏加载状态');
            document.getElementById('loading').style.display = 'none';
            document.querySelectorAll('.btn').forEach(btn => btn.disabled = false);
        }
        
        // 显示结果
        function showResult(title, content) {
            console.log('显示结果:', title);
            document.getElementById('resultTitle').textContent = title;
            document.getElementById('resultContent').innerHTML = content;
            document.getElementById('resultSection').style.display = 'block';
        }
        
        // 测试按钮功能
        function testButton(action) {
            console.log('按钮被点击:', action);
            
            const input = document.getElementById('videoUrl').value.trim();
            if (!input) {
                showResult('提示', '<div class="error-message">❌ 请输入抖音视频链接</div>');
                return;
            }
            
            showLoading();
            
            // 模拟处理过程
            setTimeout(() => {
                hideLoading();
                
                if (action === '获取视频链接') {
                    const mockUrl = 'https://example.com/mock-video-url.mp4';
                    const content = 
                        '<div class="success-message">✅ 视频链接获取成功（测试模式）</div>' +
                        '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px;">' +
                            '<strong>下载链接：</strong><br>' +
                            '<code style="word-break: break-all;">' + mockUrl + '</code><br>' +
                            '<button onclick="copyToClipboard(\\''+mockUrl+'\\')" style="margin-top: 10px; padding: 8px 15px; background: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">复制链接</button>' +
                        '</div>';
                    showResult('视频链接', content);
                } else {
                    const content = 
                        '<div class="success-message">✅ 视频详细信息获取成功（测试模式）</div>' +
                        '<div style="margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px;">' +
                            '<div style="margin-bottom: 10px;"><strong>视频ID：</strong> 7123456789012345678</div>' +
                            '<div style="margin-bottom: 10px;"><strong>标题：</strong> 这是一个测试视频标题</div>' +
                            '<div style="margin-bottom: 10px;"><strong>作者：</strong> 测试用户</div>' +
                            '<div style="margin-bottom: 10px;"><strong>点赞数：</strong> 12,345</div>' +
                            '<div style="margin-bottom: 10px;"><strong>评论数：</strong> 678</div>' +
                            '<div style="margin-bottom: 10px;"><strong>分享数：</strong> 234</div>' +
                        '</div>';
                    showResult('视频详细信息', content);
                }
            }, 1500);
        }
        
        // 复制到剪贴板
        function copyToClipboard(text) {
            console.log('复制到剪贴板:', text);
            try {
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(function() {
                        alert('已复制到剪贴板！');
                    });
                } else {
                    // 降级方案
                    const textArea = document.createElement('textarea');
                    textArea.value = text;
                    document.body.appendChild(textArea);
                    textArea.select();
                    document.execCommand('copy');
                    document.body.removeChild(textArea);
                    alert('已复制到剪贴板！');
                }
            } catch (err) {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制');
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');
            
            // 回车键支持
            const urlInput = document.getElementById('videoUrl');
            if (urlInput) {
                urlInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        testButton('获取视频链接');
                    }
                });
                
                // 自动聚焦输入框
                urlInput.focus();
            }
            
            // 显示初始提示
            setTimeout(() => {
                showResult('欢迎使用', '<div class="success-message">✅ 界面加载完成！当前为测试模式，可以测试所有界面功能。</div>');
            }, 500);
        });
    </script>
</body>
</html>
  `;
}

// 创建HTTP服务器
const server = http.createServer((req, res) => {
  console.log("Method:", req.method, "URL:", req.url);
  
  // 设置CORS头
  res.setHeader("Access-Control-Allow-Origin", "*");
  res.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
  res.setHeader("Access-Control-Allow-Headers", "Content-Type");
  
  // 处理OPTIONS请求
  if (req.method === "OPTIONS") {
    res.writeHead(200);
    res.end();
    return;
  }
  
  // 返回HTML页面
  res.writeHead(200, { "Content-Type": "text/html; charset=utf-8" });
  res.end(generateHTML());
});

const PORT = 3000;
server.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log('请在浏览器中打开上述地址查看效果');
});
